# Tracking Categories API Optimization

## Overview
This document outlines the optimizations made to handle tracking categories API calls more efficiently and address 429 rate limit errors in the Balance Sheet service.

## Problem Statement
- **Excessive API Calls**: The `getXeroTrackingCategories` function was being called for every month during balance sheet synchronization, resulting in unnecessary API calls (e.g., 60 calls for 60 months of data).
- **429 Rate Limit Errors**: Frequent API calls were causing rate limit exceeded errors from Xero API.
- **Performance Impact**: Redundant API calls were slowing down the synchronization process.

## Solution Implemented

### 1. Tracking Categories Caching
- **Singleton Cache**: Implemented a tenant-based cache that stores tracking categories per Xero tenant.
- **TTL Management**: Cache expires after 30 minutes (configurable via `TRACKING_CATEGORIES_CACHE_TTL_MS`).
- **Token Validation**: Cache is invalidated when access token changes.
- **Fallback Strategy**: Uses expired cache data if API call fails, ensuring resilience.

### 2. Enhanced Rate Limiting
- **Increased Delays**: 
  - API delay between calls: `1000ms` → `2000ms`
  - API timeout: `30000ms` → `45000ms`
- **Reduced Concurrency**: Regular sync concurrency reduced from `2` to `1` to avoid rate limits.
- **Exponential Backoff**: Implemented retry logic with exponential backoff for 429 errors.

### 3. Improved Error Handling
- **Retry Logic**: Added retry mechanism with up to 3 attempts for tracking categories API calls.
- **Smart Backoff**: Uses `retry-after` header value with exponential backoff fallback.
- **Graceful Degradation**: Continues operation with empty tracking categories if API fails.

## Code Changes

### Files Modified
1. **`balanceSheetService.ts`**:
   - Added tracking categories cache implementation
   - Updated `PRODUCTION_CONFIG` with new rate limits
   - Modified `getBalanceSheet` to use cached tracking categories
   - Added cache management functions

2. **`getXeroTrackingCategories.ts`**:
   - Added retry logic with exponential backoff
   - Enhanced error handling for 429 errors
   - Improved logging and monitoring

3. **`axiosInstance.ts`**:
   - Increased timeout to 45 seconds

### New Functions Added
- `getCachedTrackingCategories()`: Retrieves cached or fresh tracking categories
- `clearTrackingCategoriesCache()`: Clears cache for specific tenant or all tenants
- `getTrackingCacheStatistics()`: Provides cache monitoring statistics

## Configuration Updates

```typescript
const PRODUCTION_CONFIG = {
    // API Rate Limiting - Updated for 429 error handling
    XERO_API_DELAY_MS: 2000,           // Increased from 1000ms
    API_TIMEOUT_MS: 45000,             // Increased from 30000ms
    TRACKING_CATEGORIES_CACHE_TTL_MS: 30 * 60 * 1000, // 30 minutes cache

    // Concurrency Control - Reduced for better rate limit handling
    REGULAR_SYNC_CONCURRENCY: 1,       // Reduced from 2
};
```

## Benefits

### Performance Improvements
- **Reduced API Calls**: From N calls (where N = number of months) to 1 call per tenant per 30 minutes
- **Faster Sync**: Eliminates redundant API calls during multi-month synchronization
- **Better Resource Utilization**: Reduced network overhead and API quota usage

### Reliability Improvements
- **429 Error Mitigation**: Exponential backoff and reduced concurrency prevent rate limit errors
- **Resilient Caching**: Fallback to expired cache data ensures continuity
- **Enhanced Monitoring**: Cache statistics provide visibility into performance

### Operational Benefits
- **Configurable Settings**: Easy to adjust rate limits and cache TTL based on API limits
- **Cache Management**: Functions to clear cache for troubleshooting
- **Better Logging**: Detailed logs for cache hits, misses, and API calls

## Monitoring and Maintenance

### Cache Statistics
Use `getTrackingCacheStatistics()` to monitor:
- Number of cached tenants
- Cache entry ages
- Expired cache entries
- Categories count per tenant

### Cache Management
- **Clear Specific Tenant**: `clearTrackingCategoriesCache(tenantId)`
- **Clear All Cache**: `clearTrackingCategoriesCache()`

### Recommended Monitoring
- Track 429 error rates
- Monitor cache hit/miss ratios
- Watch API call frequency
- Alert on cache failures

## Testing
- Added unit tests for cache functionality
- Created integration test helpers
- Verified cache behavior with different scenarios

## Future Enhancements
1. **Persistent Cache**: Consider Redis or database caching for multi-instance deployments
2. **Adaptive Rate Limiting**: Dynamic adjustment based on API response headers
3. **Cache Warming**: Pre-populate cache for frequently accessed tenants
4. **Metrics Integration**: CloudWatch or similar monitoring integration

## Deployment Notes
- No breaking changes to existing API
- Backward compatible with existing configurations
- Cache starts empty and populates on first use
- Monitor initial deployment for 429 error reduction
