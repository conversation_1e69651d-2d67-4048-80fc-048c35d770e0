/**
 * Xero Profit & Loss Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Profit & Loss data from Xero API with intelligent
 * sync strategy based on existing data. It implements dual storage architecture:
 *
 * Storage Tables:
 * - ProfitLossTracking: Detailed data WITH tracking categories (dimensional reporting)
 * - ProfitLoss: Aggregated data WITHOUT tracking categories (summary reporting)
 *
 * Sync Strategy:
 * - Initial Sync: 5 years (60 months) of historical data when no data exists
 * - Regular Sync: 13 months (financial year + 1 month) for ongoing updates
 *
 * Key Features:
 * - Intelligent sync period determination based on existing data
 * - Strict rate limiting (1 concurrent for initial, 2 for regular sync)
 * - Dual API calls per month (with/without tracking categories)
 * - Atomic database operations with comprehensive error handling
 * - OAuth token refresh and validation
 * - Data deduplication and integrity checks
 *
 * Date Range Logic:
 * - Initial Sync: LAST 5 years from current date (60 months)
 * - Regular Sync: Financial year start to end + 1 month (13 months)
 * - Example for current date Jan 2025, FY ending 31/3/2025:
 *   * Initial: Jan 2020 to Dec 2024 (last 60 months from current date)
 *   * Regular: Mar 2024 to Mar 2025 (13 months)
 *
 * <AUTHOR> Integration Team
 * @version 2.1.0
 * @since 2024-07-04
 */

import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import {
  extractMonthlyData,
  extractMonthlyDataWithoutTracking,
  ProcessedRowDataWithoutTracking,
} from '../services/extractMonthlyData';
import moment from 'moment';
import { getXeroConfig } from '../config/environment';
import { XeroRequestData, ProcessedRowData, ValidationError, XeroError } from '../types';
import { getXeroTrackingCategories } from '@/services/getXeroTrackingCategories';
import {
  createSyncLog,
  markSyncStarted,
  markSyncSuccess,
  markSyncFailed,
  SyncLogData
} from '../services/syncLogService';
import {
  logSuccessfulApiCall,
  logFailedApiCall
} from '../services/apiLogService';

/**
 * Production Configuration Constants
 *
 * These constants control the behavior of the Profit & Loss synchronization
 * service in production environments. Adjusted based on Balance Sheet optimizations
 * and Xero API limits to prevent 429 rate limit errors.
 */
const PRODUCTION_CONFIG = {
  // API Rate Limiting - Updated for 429 error handling
  XERO_API_DELAY_MS: 2000,           // Increased delay between dual API calls (2 seconds)
  API_TIMEOUT_MS: 45000,             // Increased API request timeout (45 seconds)
  TRACKING_CATEGORIES_CACHE_TTL_MS: 30 * 60 * 1000, // Cache tracking categories for 30 minutes

  // Sync Strategy
  INITIAL_SYNC_MONTHS: 60,           // 5 years of historical data for new companies
  REGULAR_SYNC_MONTHS: 13,           // 13 months for existing companies

  // Concurrency Control - Reduced for better rate limit handling
  INITIAL_SYNC_CONCURRENCY: 1,       // Single concurrent request for initial sync
  REGULAR_SYNC_CONCURRENCY: 1,       // Reduced from 2 to 1 for regular sync
};

/**
 * Tracking Categories Cache
 *
 * In-memory cache to store tracking categories per tenant to avoid
 * repeated API calls and reduce rate limiting issues.
 */
interface TrackingCategoriesCache {
  [tenantId: string]: {
    categories: any[];
    timestamp: number;
    accessToken: string;
  };
}

const trackingCategoriesCache: TrackingCategoriesCache = {};

/**
 * Get cached tracking categories or fetch from API if not cached/expired
 *
 * @param accessToken - Valid Xero OAuth access token
 * @param tenantId - Xero tenant identifier
 * @returns Promise<any[]> - Array of tracking categories
 */
async function getCachedTrackingCategories(
  accessToken: string,
  tenantId: string
): Promise<any[]> {
  const now = Date.now();
  const cached = trackingCategoriesCache[tenantId];

  // Check if cache is valid and token hasn't changed
  if (cached &&
    (now - cached.timestamp) < PRODUCTION_CONFIG.TRACKING_CATEGORIES_CACHE_TTL_MS &&
    cached.accessToken === accessToken) {
    console.log(`📋 Using cached tracking categories for tenant: ${tenantId.substring(0, 8)}... (${cached.categories.length} categories)`);
    return cached.categories;
  }

  // Cache miss or expired - fetch from API
  console.log(`🔄 Fetching tracking categories from API for tenant: ${tenantId.substring(0, 8)}...`);
  try {
    const categories = await getXeroTrackingCategories(accessToken, tenantId);

    // Update cache
    trackingCategoriesCache[tenantId] = {
      categories,
      timestamp: now,
      accessToken
    };

    console.log(`✅ Cached ${categories.length} tracking categories for tenant: ${tenantId.substring(0, 8)}...`);
    return categories;
  } catch (error: any) {
    console.warn(`⚠️ Failed to fetch tracking categories: ${error.message}`);
    // Return cached data if available, even if expired, as fallback
    if (cached) {
      console.log(`📋 Using expired cached tracking categories as fallback for tenant: ${tenantId.substring(0, 8)}...`);
      return cached.categories;
    }

    // No cache available, return empty array to continue without tracking
    console.warn('⚠️ No cached tracking categories available, continuing without tracking categories');
    return [];
  }
}

/**
 * Clear tracking categories cache for a specific tenant or all tenants
 *
 * @param tenantId - Optional tenant ID to clear specific cache entry
 */
function clearTrackingCategoriesCache(tenantId?: string): void {
  if (tenantId) {
    delete trackingCategoriesCache[tenantId];
    console.log(`🗑️ Cleared tracking categories cache for tenant: ${tenantId.substring(0, 8)}...`);
  } else {
    Object.keys(trackingCategoriesCache).forEach(key => delete trackingCategoriesCache[key]);
    console.log('🗑️ Cleared all tracking categories cache');
  }
}

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

/**
 * Prisma Database Client Management
 *
 * Implements singleton pattern for database connections to optimize
 * Lambda cold starts and connection pooling.
 */
let prisma: PrismaClient | null = null;

/**
 * Get or create Prisma client instance
 * @returns {PrismaClient} Singleton Prisma client instance
 */
function getPrismaClient(): PrismaClient {
  if (!prisma) {
    const config = {
      // Only log errors and warnings in production
      log: ['error', 'warn'] as Array<'error' | 'warn'>,
      errorFormat: 'pretty' as const,
      // Configure transaction settings for better performance
      transactionOptions: {
        maxWait: 10000, // 10 seconds max wait to acquire transaction
        timeout: 60000, // 60 seconds transaction timeout
      },
    };
    // Handle offline development environment
    if (process.env.IS_OFFLINE === 'true') {
      delete process.env.PRISMA_QUERY_ENGINE_LIBRARY;
    }
    prisma = new PrismaClient(config);
  }
  return prisma;
}

/**
 * AWS Lambda Handler for Xero Profit & Loss Synchronization
 *
 * Supports both API Gateway (HTTP) and SQS (queue) event sources.
 * Processes requests to sync 13 months of Profit & Loss data from Xero.
 *
 * @param {APIGatewayProxyEvent | SQSEvent} event - AWS Lambda event
 * @param {Context} context - AWS Lambda context
 * @returns {Promise<APIGatewayProxyResult | void>} HTTP response or void for SQS
 */
export const handler = async (
  event: APIGatewayProxyEvent | SQSEvent,
  context: Context
): Promise<APIGatewayProxyResult | void> => {
  if ('Records' in event && event.Records?.length) {
    // Handle SQS event - process each record in the batch
    for (const record of event.Records) {
      try {
        const requestData = JSON.parse(record.body);
        await processRequest(requestData, context);
      } catch (err) {
        // Re-throw error to trigger SQS retry mechanism
        throw err;
      }
    }
    return;
  } else {
    // Handle API Gateway event - direct HTTP request
    try {
      const requestData = parseRequestData(event);
      await processRequest(requestData, context);

      return {
        statusCode: 200,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          success: true,
          message: 'Profit & Loss data processed successfully',
          timestamp: new Date().toISOString(),
        }),
      };
    } catch (err: any) {
      return {
        statusCode: err instanceof ValidationError ? 400 : 500,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          success: false,
          error: err.message,
          type: err.constructor.name,
        }),
      };
    }
  }
};

/**
 * Parse Request Data from Event
 *
 * Extracts and parses request data from various AWS event sources:
 * - API Gateway events (HTTP requests)
 * - SQS events (queue messages)
 * - Generic events with body property
 *
 * @param {APIGatewayProxyEvent | SQSEvent | Object} event - AWS event object
 * @returns {XeroRequestData} Parsed request data
 * @throws {ValidationError} When request data is invalid or missing
 */
export function parseRequestData(
  event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
  // handle SQS
  if ('Records' in event && event.Records?.[0]) {
    try {
      const parsed = JSON.parse(event.Records[0].body);
      return parsed as XeroRequestData;
    } catch {
      throw new ValidationError('Failed to parse SQS message body');
    }
  }

  // handle API Gateway
  if ('body' in event && event.body) {
    try {
      const parsed = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
      return parsed as XeroRequestData;
    } catch {
      throw new ValidationError('Failed to parse API Gateway body');
    }
  }

  throw new ValidationError('Invalid request data');
}

/**
 * Main Processing Function for Xero Profit & Loss Synchronization
 *
 * Orchestrates the complete sync process:
 * 1. Validates request data and retrieves company information
 * 2. Ensures valid Xero OAuth token
 * 3. Determines sync period based on existing data
 * 4. Processes months in parallel with rate limiting
 * 5. Stores data in both ProfitLossTracking and ProfitLoss tables
 *
 * @param {XeroRequestData} requestData - Request containing companyId and tenantId
 * @param {Context} _context - AWS Lambda context (unused but required)
 * @throws {ValidationError} When request data is invalid
 * @throws {Error} When company not found or processing fails
 */
async function processRequest(requestData: XeroRequestData, _context: Context): Promise<void> {
  const startTime = Date.now();
  let syncLogId: string | null = null;

  try {
    validateRequestData(requestData);


    // Create sync log entry
    const syncLogData: SyncLogData = {
      entity: 'ProfitLoss',
      integration: 'Xero',
      companyId: requestData.companyId,
      requestPayload: {
        companyId: requestData.companyId,
      },
      maxRetries: 3,
    };

    syncLogId = await createSyncLog(syncLogData);
    console.log(`🚀 Starting Profit & Loss sync - Sync Log ID: ${syncLogId}`);

    const prisma = getPrismaClient();
    const integration: any = await getActiveIntegration(requestData);
    console.log(`🔄 Starting Profit & Loss sync for company`, integration);
    console.log(`🔄 Starting Profit & Loss sync for company`, !integration.FinancialYearEnd);
    console.log(`🔄 Starting Profit & Loss sync for company`, !integration || !integration.FinancialYearEnd);

    if (!integration || !integration.FinancialYearEnd) {
      throw new Error('User company or financial year end not found');
    }
    console.log(`🔄 Starting Profit & Loss sync for company`, integration);

    const financialYearEnd = moment(integration.FinancialYearEnd).endOf('month');

    const validIntegration = await ensureValidToken(integration);

    /**
     * Data Existence Check and Sync Strategy
     *
     * Determines sync period based on existing data:
     * - If NO data exists: Sync LAST 5 years of historical data (60 months from current date)
     * - If data exists: Sync 13 months (current financial year + 1 month)
     */
    const existingDataCount = await prisma.profitLoss.count({
      where: {
        CompanyId: requestData.companyId,
      },
    });

    const isInitialSync = existingDataCount === 0;
    const syncPeriodMonths = isInitialSync ? 60 : 13; // Last 5 years or 13 months
    const syncDescription = isInitialSync
      ? 'last 5 years (initial sync)'
      : '13 months (regular sync)';

    console.log(
      `Data sync strategy: ${syncDescription} - Found ${existingDataCount} existing records`
    );

    /**
     * Generate Date Ranges Based on Sync Strategy
     *
     * Creates date ranges for the determined sync period:
     * - Initial sync: LAST 60 months (5 years) from current date
     * - Regular sync: 13 months starting from financial year start
     *
     * Example for current date Jan 2025, FY ending 31/3/2025:
     * - Initial: Jan 2020 to Dec 2024 (last 60 months from current date)
     * - Regular: Mar 2024 to Mar 2025 (13 months)
     */
    const monthRanges: Array<{
      startDate: string; // YYYY-MM-DD format for API calls
      endDate: string; // YYYY-MM-DD format for API calls
      monthDate: moment.Moment; // Moment object for calculations
      year: number; // Calendar year
      month: number; // Calendar month (1-12)
    }> = [];

    // Calculate start date based on sync strategy
    const syncStartDate = isInitialSync
      ? moment()
        .subtract(syncPeriodMonths - 1, 'months')
        .startOf('month') // Last 5 years from current date
      : financialYearEnd.clone().subtract(12, 'months').startOf('month'); // 13 months (financial year start)

    // Generate consecutive months for the sync period
    const currentMonth = moment().startOf('month');

    for (let i = 0; i < syncPeriodMonths; i++) {
      const monthDate = syncStartDate.clone().add(i, 'months');

      // Skip future months (don't sync data that doesn't exist yet)
      if (monthDate.isAfter(currentMonth)) {
        break;
      }

      const startDate = monthDate.clone().startOf('month').format('YYYY-MM-DD');
      const endDate = monthDate.clone().endOf('month').format('YYYY-MM-DD');

      monthRanges.push({
        startDate,
        endDate,
        monthDate: monthDate.clone(),
        year: monthDate.year(),
        month: monthDate.month() + 1, // Convert from zero-based to 1-based month
      });
    }

    /**
     * Month Processor Function with Rate Limiting
     *
     * Processes a single month's data by making dual API calls to Xero:
     * 1. Fetches detailed data WITH tracking categories
     * 2. Small delay to respect API rate limits
     * 3. Fetches aggregated data WITHOUT tracking categories
     *
     * @param {Object} monthData - Month information with date range
     * @returns {Promise<Object>} Processed data for both storage tables
     */
    const monthProcessor = async (
      monthData: (typeof monthRanges)[0]
    ): Promise<{
      dataWithTracking: ProcessedRowData[];
      dataWithoutTracking: ProcessedRowDataWithoutTracking[];
      monthInfo: typeof monthData;
    }> => {
      const monthlyRequestData: XeroRequestData = {
        ...requestData,
        startDate: monthData.startDate,
        endDate: monthData.endDate,
      };

      // Fetch data with tracking categories
      const profitLossDataWithTracking = await getProfitLoss(
        validIntegration.XeroAccessToken!,
        validIntegration.XeroTenantId!,
        monthlyRequestData
      );

      // Rate-limited delay between API calls
      // Increased delay to handle 429 rate limit errors more effectively
      console.log(`⏱️ Rate limiting delay (${PRODUCTION_CONFIG.XERO_API_DELAY_MS}ms) before second API call...`);
      await new Promise(resolve => setTimeout(resolve, PRODUCTION_CONFIG.XERO_API_DELAY_MS));

      // Fetch data without tracking categories
      const profitLossDataWithoutTracking = await getProfitLossWithoutTracking(
        validIntegration.XeroAccessToken!,
        validIntegration.XeroTenantId!,
        monthlyRequestData
      );

      return {
        dataWithTracking: profitLossDataWithTracking || [],
        dataWithoutTracking: profitLossDataWithoutTracking || [],
        monthInfo: monthData,
      };
    };

    /**
     * Parallel Processing with Enhanced Xero API Rate Limiting
     *
     * Xero API Limits (Official):
     * - 60 requests per minute per organization
     * - 1000 requests per day per organization (rolling 24-hour period)
     *
     * Enhanced Strategy (Based on Balance Sheet optimizations):
     * - Initial sync (60 months): 1 concurrent request (120 API calls total)
     * - Regular sync (13 months): 1 concurrent request (26 API calls total) - Reduced from 2
     * - 2-second delays between API calls to prevent 429 errors
     * - Tracking categories caching to reduce API calls
     *
     * This ensures we never exceed Xero's strict rate limits and avoid 429 errors.
     */
    const concurrency = isInitialSync
      ? PRODUCTION_CONFIG.INITIAL_SYNC_CONCURRENCY
      : PRODUCTION_CONFIG.REGULAR_SYNC_CONCURRENCY;
    const limit = require('p-limit')(concurrency);

    console.log(`⚙️ Using concurrency level: ${concurrency} (${isInitialSync ? 'initial' : 'regular'} sync)`);

    const totalApiCalls = syncPeriodMonths * 2;
    console.log(
      `Using ${concurrency} concurrent requests for ${syncPeriodMonths} months (${totalApiCalls} total API calls)`
    );

    // Calculate estimated execution time (2 API calls per month + 1 second delay between them)
    const estimatedMinutes = Math.ceil(((syncPeriodMonths * 2) / concurrency / 60) * 2); // Rough estimate
    console.log(`Estimated execution time: ~${estimatedMinutes} minutes`);

    // Warn if approaching daily limit
    if (totalApiCalls > 800) {
      console.warn(`⚠️  High API usage: ${totalApiCalls} calls may approach Xero's 1000/day limit`);
    }

    // Log processing start for monitoring
    console.log(
      `Starting ${syncDescription} sync: Processing ${monthRanges.length} months from ${monthRanges[0]?.startDate} to ${monthRanges[monthRanges.length - 1]?.endDate}`
    );

    // Mark sync as started
    if (syncLogId) {
      await markSyncStarted(syncLogId, 'Reports/ProfitAndLoss', 'GET');
    }

    const monthTasks = monthRanges.map(monthData =>
      limit(async () => {
        try {
          return await monthProcessor(monthData);
        } catch (error) {
          return {
            dataWithTracking: [],
            dataWithoutTracking: [],
            monthInfo: monthData,
            error: error instanceof Error ? error : new Error(String(error)),
          };
        }
      })
    );

    const results = await Promise.all(monthTasks);

    /**
     * Database Operations - Dual Storage Implementation
     *
     * Processes results and stores data in both tables:
     * - ProfitLossTracking: Detailed data with tracking categories
     * - ProfitLoss: Aggregated data without tracking categories
     *
     * Uses atomic operations to ensure data consistency.
     */
    let totalProcessed = 0;
    let totalErrors = 0;

    for (const result of results) {
      // Skip failed results
      if ('error' in result) {
        totalErrors++;
        console.error(
          `Failed to process month ${result.monthInfo.monthDate.format('MMM YYYY')}:`,
          result.error
        );
        continue;
      }

      // Skip months with no data
      if (!result.dataWithTracking.length && !result.dataWithoutTracking.length) {
        console.log(`No data found for ${result.monthInfo.monthDate.format('MMM YYYY')}`);
        continue;
      }

      // Store data in database if requested
      if (requestData.dumpToDatabase) {
        const { year, month } = result.monthInfo;

        /**
         * Data Cleanup - Remove existing data for the month
         *
         * Ensures data integrity by removing any existing records
         * for the same company, year, and month before inserting new data.
         */
        await Promise.all([
          prisma.profitLossTracking.deleteMany({
            where: {
              CompanyId: requestData.companyId,
              Year: year,
              Month: month,
            },
          }),
          prisma.profitLoss.deleteMany({
            where: {
              CompanyId: requestData.companyId,
              Year: year,
              Month: month,
            },
          }),
        ]);

        /**
         * Data Insertion - Dual Storage Implementation with Batch Processing
         *
         * Inserts processed data into both tables with batch processing:
         * - ProfitLossTracking: Detailed records with tracking categories
         * - ProfitLoss: Aggregated records without tracking categories
         *
         * Uses batch processing to handle large datasets and prevent timeouts.
         */
        const insertPromises = [];

        // Process tracking data in batches
        if (result.dataWithTracking.length > 0) {
          const batchSize = 100;
          const trackingBatches = Math.ceil(result.dataWithTracking.length / batchSize);

          for (let i = 0; i < trackingBatches; i++) {
            const start = i * batchSize;
            const end = Math.min(start + batchSize, result.dataWithTracking.length);
            const batch = result.dataWithTracking.slice(start, end);

            insertPromises.push(
              prisma.profitLossTracking.createMany({
                data: batch,
                skipDuplicates: true,
              })
            );
          }
        }

        // Process summary data in batches
        if (result.dataWithoutTracking.length > 0) {
          const batchSize = 100;
          const summaryBatches = Math.ceil(result.dataWithoutTracking.length / batchSize);

          for (let i = 0; i < summaryBatches; i++) {
            const start = i * batchSize;
            const end = Math.min(start + batchSize, result.dataWithoutTracking.length);
            const batch = result.dataWithoutTracking.slice(start, end);

            insertPromises.push(
              prisma.profitLoss.createMany({
                data: batch,
                skipDuplicates: true,
              })
            );
          }
        }

        await Promise.all(insertPromises);

        // Log successful processing for monitoring
        console.log(
          `Successfully processed ${result.monthInfo.monthDate.format('MMM YYYY')}: ${result.dataWithTracking.length} detailed records, ${result.dataWithoutTracking.length} summary records`
        );
        totalProcessed++;
      }
    }

    // Log final processing summary for monitoring
    console.log(
      `${syncDescription} completed: ${totalProcessed} months processed successfully, ${totalErrors} errors`
    );

    if (totalProcessed === 0 && totalErrors > 0) {
      throw new Error(`Failed to process any months successfully. ${totalErrors} errors occurred.`);
    }

    // Mark sync as successful
    if (syncLogId) {
      const duration = Date.now() - startTime;
      await markSyncSuccess(
        syncLogId,
        duration,
        `Profit & Loss sync completed successfully: ${totalProcessed} months processed, ${totalErrors} errors`,
        {
          monthsProcessed: totalProcessed,
          totalErrors: totalErrors,
          syncType: syncDescription,
          duration: `${duration}ms`
        }
      );
    }

    console.log(`✅ Profit & Loss sync completed successfully in ${Date.now() - startTime}ms`);
  } catch (error: any) {
    // Mark sync as failed
    if (syncLogId) {
      const duration = Date.now() - startTime;
      await markSyncFailed(syncLogId, duration, error, 0);
    }

    console.error(`❌ Profit & Loss sync failed after ${Date.now() - startTime}ms:`, {
      error: error.message,
      companyId: requestData.companyId,
    });
    throw error;
  }
}

/**
 * Request Data Validation
 *
 * Validates that all required fields are present in the request data.
 * Throws ValidationError with specific missing field information.
 *
 * @param {XeroRequestData} data - Request data to validate
 * @throws {ValidationError} When required fields are missing
 */
function validateRequestData(data: XeroRequestData): void {
  const missing: string[] = [];
  if (!data?.companyId) missing.push('Company Id');
  if (missing.length) {
    throw new ValidationError(`${missing.join(' and ')} missing`, missing);
  }
}

/**
 * Get Active Xero Integration
 *
 * Retrieves the active Xero integration for the specified company.
 * Only returns companies with ACTIVE connection status.
 *
 * @param {XeroRequestData} data - Request data containing companyId
 * @returns {Promise<Company>} Active company integration
 * @throws {Error} When no active integration is found
 */
async function getActiveIntegration(data: XeroRequestData): Promise<Company> {
  const integration = await getPrismaClient().company.findFirst({
    where: {
      Id: data.companyId,
      ConnectionStatus: 'ACTIVE',
    },
  });
  if (!integration) {
    throw new XeroError('Xero integration not found or inactive', 404);
  }
  return integration;
}

/**
 * OAuth Token Validation and Refresh
 *
 * Ensures the Xero access token is valid and refreshes it if it expires
 * within the next 10 minutes to prevent API call failures.
 *
 * @param {Company} integration - Company integration with token information
 * @returns {Promise<Company>} Updated integration with valid token
 */
async function ensureValidToken(integration: Company): Promise<Company> {
  const expiresAt = new Date(integration.XeroTokenExpiry!);
  // Refresh token if it expires within 10 minutes
  if (expiresAt.getTime() - Date.now() <= 10 * 60 * 1000) {
    return await refreshXeroToken(integration);
  }
  return integration;
}

/**
 * Fetch Profit & Loss Data WITH Tracking Categories
 *
 * Retrieves detailed Profit & Loss report from Xero API including
 * tracking category dimensions for comprehensive reporting.
 *
 * @param {string} accessToken - Valid Xero OAuth access token
 * @param {string} tenantId - Xero tenant identifier
 * @param {XeroRequestData} requestData - Request parameters including date range
 * @returns {Promise<ProcessedRowData[]>} Processed P&L data with tracking categories
 */
const getProfitLoss = async (
  accessToken: string,
  tenantId: string,
  requestData: XeroRequestData
): Promise<ProcessedRowData[]> => {
  const startTime = Date.now();
  console.log(`🔍 Fetching Profit & Loss WITH tracking categories for date range: ${requestData.startDate} to ${requestData.endDate}`);

  const { baseUrl } = getXeroConfig();

  // Use cached tracking categories to reduce API calls
  const trackingCategories: any = await getCachedTrackingCategories(accessToken, tenantId);
  let params = '';
  if (trackingCategories.length > 0) {
    trackingCategories.forEach((category: any, index: number) => {
      if (index === 0) {
        params += `trackingCategoryID=${category.categoryId}`;
      } else {
        params += `&trackingCategoryID${index + 1}=${category.categoryId}`;
      }
    });
  }

  const url = `${baseUrl}Reports/ProfitAndLoss?fromDate=${requestData.startDate}&toDate=${requestData.endDate}&${params}`;

  try {
    console.log(`🌐 API Request URL: ${url.replace(accessToken, '[REDACTED]')}`);

    const res = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Xero-tenant-id': tenantId,
        Accept: 'application/json',
        'User-Agent': 'ProfitLossSync/2.0.0',
      },
      timeout: PRODUCTION_CONFIG.API_TIMEOUT_MS, // Configurable API timeout
    });

    const requestTime = Date.now() - startTime;
    console.log(`✅ Profit & Loss API call (with tracking) completed in ${requestTime}ms`);

    const reportData = res?.data;
    if (!reportData || !reportData.Reports || !reportData.Reports[0]) {
      throw new Error('Invalid Profit and Loss data structure received from Xero');
    }

    // Log successful API call
    await logSuccessfulApiCall(
      requestData.companyId,
      'GET',
      url.replace(accessToken, '[REDACTED]'),
      requestTime,
      'ProfitLoss',
      {
        startDate: requestData.startDate,
        endDate: requestData.endDate,
        tenantId: tenantId.substring(0, 8) + '...',
        withTracking: true
      },
      { recordCount: reportData.Reports[0]?.Rows?.length || 0 }
    );

    // Map the extracted data to include required ProcessedRowData fields with default or derived values
    return extractMonthlyData(reportData.Reports[0], trackingCategories, requestData);
  } catch (error: any) {
    const requestTime = Date.now() - startTime;
    console.error(`❌ Profit & Loss API call (with tracking) failed after ${requestTime}ms:`, {
      error: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      startDate: requestData.startDate,
      endDate: requestData.endDate,
      tenantId: tenantId.substring(0, 8) + '...',
    });

    // Log failed API call
    await logFailedApiCall(
      requestData.companyId,
      'GET',
      url.replace(accessToken, '[REDACTED]'),
      requestTime,
      'ProfitLoss',
      error,
      {
        startDate: requestData.startDate,
        endDate: requestData.endDate,
        tenantId: tenantId.substring(0, 8) + '...',
        withTracking: true
      }
    );

    // Handle Xero rate limit errors specifically
    if (error.response?.status === 429) {
      const retryAfter = error.response.headers['retry-after'] || 60;
      throw new Error(
        `Xero API rate limit exceeded. Retry after ${retryAfter} seconds. Consider reducing concurrency.`
      );
    }
    // Re-throw other errors
    throw error;
  }
};

/**
 * Fetch Profit & Loss Data WITHOUT Tracking Categories
 *
 * Retrieves aggregated Profit & Loss report from Xero API without
 * tracking category dimensions for summary reporting purposes.
 *
 * @param {string} accessToken - Valid Xero OAuth access token
 * @param {string} tenantId - Xero tenant identifier
 * @param {XeroRequestData} requestData - Request parameters including date range
 * @returns {Promise<ProcessedRowDataWithoutTracking[]>} Processed aggregated P&L data
 */
const getProfitLossWithoutTracking = async (
  accessToken: string,
  tenantId: string,
  requestData: XeroRequestData
): Promise<ProcessedRowDataWithoutTracking[]> => {
  const startTime = Date.now();
  console.log(`🔍 Fetching Profit & Loss WITHOUT tracking categories for date range: ${requestData.startDate} to ${requestData.endDate}`);

  const { baseUrl } = getXeroConfig();

  // No tracking category parameters for this request
  const url = `${baseUrl}Reports/ProfitAndLoss?fromDate=${requestData.startDate}&toDate=${requestData.endDate}`;

  try {
    console.log(`🌐 API Request URL: ${url.replace(accessToken, '[REDACTED]')}`);

    const res = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Xero-tenant-id': tenantId,
        Accept: 'application/json',
        'User-Agent': 'ProfitLossSync/2.0.0',
      },
      timeout: PRODUCTION_CONFIG.API_TIMEOUT_MS, // Configurable API timeout
    });

    const requestTime = Date.now() - startTime;
    console.log(`✅ Profit & Loss API call (without tracking) completed in ${requestTime}ms`);

    const reportData = res?.data;
    if (!reportData || !reportData.Reports || !reportData.Reports[0]) {
      throw new Error('Invalid Profit and Loss data structure received from Xero');
    }

    // Log successful API call
    await logSuccessfulApiCall(
      requestData.companyId,
      'GET',
      url.replace(accessToken, '[REDACTED]'),
      requestTime,
      'ProfitLoss',
      {
        startDate: requestData.startDate,
        endDate: requestData.endDate,
        tenantId: tenantId.substring(0, 8) + '...',
        withTracking: false
      },
      { recordCount: reportData.Reports[0]?.Rows?.length || 0 }
    );

    // Extract data without tracking categories (aggregated by account)
    return extractMonthlyDataWithoutTracking(reportData.Reports[0], requestData);
  } catch (error: any) {
    const requestTime = Date.now() - startTime;
    console.error(`❌ Profit & Loss API call (without tracking) failed after ${requestTime}ms:`, {
      error: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      startDate: requestData.startDate,
      endDate: requestData.endDate,
      tenantId: tenantId.substring(0, 8) + '...',
    });

    // Log failed API call
    await logFailedApiCall(
      requestData.companyId,
      'GET',
      url.replace(accessToken, '[REDACTED]'),
      requestTime,
      'ProfitLoss',
      error,
      {
        startDate: requestData.startDate,
        endDate: requestData.endDate,
        tenantId: tenantId.substring(0, 8) + '...',
        withTracking: false
      }
    );

    // Handle Xero rate limit errors specifically
    if (error.response?.status === 429) {
      const retryAfter = error.response.headers['retry-after'] || 60;
      throw new Error(
        `Xero API rate limit exceeded. Retry after ${retryAfter} seconds. Consider reducing concurrency.`
      );
    }
    // Re-throw other errors
    throw error;
  }
};
