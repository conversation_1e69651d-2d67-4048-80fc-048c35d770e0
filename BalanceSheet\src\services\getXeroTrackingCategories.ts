import axios from '../utils/axiosInstance';
import { getXeroConfig } from '../config/environment';

export interface XeroTrackingCategory {
    categoryId: string;
    name: string;
    status: string;
    options?: Array<{
        trackingOptionId: string;
        name: string;
        status: string;
    }>;
}

/**
 * Fetch tracking categories from Xero API with retry logic for rate limiting
 *
 * @param accessToken - Valid Xero OAuth access token
 * @param tenantId - Xero tenant identifier
 * @param maxRetries - Maximum number of retry attempts (default: 3)
 * @returns Promise<XeroTrackingCategory[]> - Array of tracking categories
 */
export async function getXeroTrackingCategories(
    accessToken: string,
    tenantId: string,
    maxRetries: number = 3
): Promise<XeroTrackingCategory[]> {
    const { baseUrl } = getXeroConfig();
    const url = `${baseUrl}TrackingCategories`;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`🔄 Fetching tracking categories (attempt ${attempt}/${maxRetries}) for tenant: ${tenantId.substring(0, 8)}...`);

            const response = await axios.get(url, {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    'Xero-tenant-id': tenantId,
                    Accept: 'application/json',
                },
                timeout: 45000, // Increased timeout to match balance sheet service
            });

            if (!response.data?.TrackingCategories) {
                console.warn('No tracking categories found in Xero response');
                return [];
            }

            const categories = response.data.TrackingCategories.map((category: any) => ({
                categoryId: category.TrackingCategoryID,
                name: category.Name,
                status: category.Status,
                options: category.Options?.map((option: any) => ({
                    trackingOptionId: option.TrackingOptionID,
                    name: option.Name,
                    status: option.Status,
                })) || []
            }));

            console.log(`✅ Successfully fetched ${categories.length} tracking categories for tenant: ${tenantId.substring(0, 8)}...`);
            return categories;

        } catch (error: any) {
            const isLastAttempt = attempt === maxRetries;

            // Handle rate limiting with exponential backoff
            if (error.response?.status === 429) {
                const retryAfter = parseInt(error.response.headers['retry-after']) || 60;
                const backoffDelay = Math.min(retryAfter * 1000, 2000 * Math.pow(2, attempt - 1)); // Exponential backoff with max from retry-after header

                console.warn(`⚠️ Rate limit hit for tracking categories (attempt ${attempt}/${maxRetries}). Retry after ${backoffDelay}ms`);

                if (!isLastAttempt) {
                    await new Promise(resolve => setTimeout(resolve, backoffDelay));
                    continue; // Retry
                } else {
                    console.error(`❌ Rate limit exceeded after ${maxRetries} attempts for tracking categories`);
                    throw new Error(
                        `Xero API rate limit exceeded while fetching tracking categories after ${maxRetries} attempts. Last retry-after: ${retryAfter} seconds.`
                    );
                }
            }

            // Handle other errors
            console.error(`❌ Failed to fetch tracking categories (attempt ${attempt}/${maxRetries}):`, {
                error: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText,
                tenantId: tenantId.substring(0, 8) + '...'
            });

            if (isLastAttempt) {
                // Return empty array for non-critical failures on final attempt
                console.warn('Continuing without tracking categories due to API error after all retries');
                return [];
            }

            // Wait before retry for non-429 errors
            const retryDelay = 1000 * attempt; // Linear backoff for other errors
            console.log(`⏳ Waiting ${retryDelay}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
    }

    // This should never be reached, but return empty array as fallback
    return [];
}
